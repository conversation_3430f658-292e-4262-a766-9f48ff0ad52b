'use client'

import { useState } from 'react'
import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CreditCard, Loader2, Lock } from 'lucide-react'
import { useTranslations, useLocale } from 'next-intl'
import { useToast } from '@/hooks/use-toast'

interface StripePaymentFormProps {
  clientSecret: string
  onSuccess: (paymentIntentId: string) => void
  onError: (error: string) => void
  amount: number
  loading?: boolean
}

const paymentElementOptions = {
  layout: 'tabs' as const,
}

export function StripePaymentForm({
  clientSecret, // eslint-disable-line @typescript-eslint/no-unused-vars
  onSuccess,
  onError,
  amount,
  loading = false
}: StripePaymentFormProps) {
  const stripe = useStripe()
  const elements = useElements()
  const t = useTranslations('checkout')
  const locale = useLocale()
  const { toast } = useToast()
  const [processing, setProcessing] = useState(false)
  const [isComplete, setIsComplete] = useState(false)
  const [message, setMessage] = useState<string | null>(null)

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements || processing || loading) {
      return
    }

    setProcessing(true)

    try {
      // Confirm the payment with the payment element
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/${locale}/checkout/success`,
        },
        redirect: 'if_required',
      })

      console.log('🛒 Payment confirmation result:', { error, paymentIntent })

      if (error) {
        console.error('Payment confirmation error:', error)
        setMessage(error.message || 'Payment failed')
        onError(error.message || 'Payment failed')
        toast({
          title: t('paymentFailed'),
          description: error.message || t('paymentError'),
          variant: 'destructive'
        })
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        onSuccess(paymentIntent.id)
        toast({
          title: t('paymentSuccess'),
          description: t('paymentSuccessDesc'),
          variant: 'default'
        })
      } else if (paymentIntent && paymentIntent.status === 'requires_action') {
        // Handle 3D Secure or other authentication
        toast({
          title: t('authRequired'),
          description: t('authRequiredDesc'),
          variant: 'default'
        })
      } else if (paymentIntent && paymentIntent.status === 'processing') {
        // Handle processing status (common with Twint and other redirect payments)
        console.log('Payment is processing, redirecting to success page...')
        onSuccess(paymentIntent.id)
        toast({
          title: t('paymentProcessing'),
          description: t('paymentProcessingDesc'),
          variant: 'default'
        })
      }
    } catch (err) {
      console.error('Payment error:', err)
      const errorMessage = err instanceof Error ? err.message : 'Payment failed'
      setMessage(errorMessage)
      onError(errorMessage)
      toast({
        title: t('paymentFailed'),
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setProcessing(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF'
    }).format(amount)
  }

  return (
    <Card className="border-2 border-primary/20 shadow-xl bg-card/95 backdrop-blur-sm">
      <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 border-b border-primary/10">
        <CardTitle className="flex items-center gap-2 text-primary">
          <CreditCard className="h-5 w-5" />
          {t('paymentDetails')}
        </CardTitle>
      </CardHeader>
      <CardContent className="bg-background/50">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="p-4 border-2 border-primary/20 rounded-lg bg-background/80 backdrop-blur-sm shadow-inner transition-all duration-200 hover:border-primary/30 focus-within:border-primary/50 focus-within:shadow-lg focus-within:shadow-primary/10">
            <PaymentElement
              options={paymentElementOptions}
              onReady={() => setIsComplete(true)}
            />
          </div>

          {message && (
            <div className="text-sm text-red-600 mt-2">
              {message}
            </div>
          )}

          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Lock className="h-4 w-4" />
              <span>{t('secureSSL')}</span>
            </div>
            <span>{t('total')}: {formatCurrency(amount)}</span>
          </div>

          <Button
            type="submit"
            className="w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
            size="lg"
            disabled={!stripe || !isComplete || processing || loading}
          >
            {processing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('processing')}
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                {t('payNow')} - {formatCurrency(amount)}
              </>
            )}
          </Button>

          <div className="text-center text-xs text-gray-500">
            <p>{t('dataProtected')}</p>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
