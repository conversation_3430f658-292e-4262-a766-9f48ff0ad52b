'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { User, Menu, X, LogOut, Settings, LayoutDashboard, Cog, ShoppingBag, Package, Gift, Heart, Info } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { createClient } from '@/lib/supabase/client';
import { safeGetUser } from '@/lib/supabase/helpers';
import { CartButton } from '@/components/cart/cart-button';
import { LanguageSwitcher } from '@/components/language-switcher';
import { usePendingGifts } from '@/hooks/use-pending-gifts';
import type { User as SupabaseUser } from '@supabase/supabase-js';
import { locales, defaultLocale, type Locale } from '@/lib/i18n/config';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const t = useTranslations('navigation');
  const locale = useLocale();
  const router = useRouter();
  const { pendingCount } = usePendingGifts(user?.id);

  const fetchIsAdmin = async (userId: string) => {
    try {
      const supabase = createClient();
      const { data } = await supabase
        .from('users')
        .select('is_admin')
        .eq('id', userId)
        .single();
      setIsAdmin(!!data?.is_admin);
    } catch (error) {
      console.error('Error fetching admin status:', error);
      setIsAdmin(false);
    }
  };

  // Ensure locale is always a valid value
  const safeLocale = locales.includes(locale as Locale) ? locale : defaultLocale;

  // Generate navigation links with proper locale handling
  const getLocalizedPath = (path: string) => {
    // Always include locale prefix
    return `/${safeLocale}${path}`;
  };

  const navigation = [
    { name: t('shop'), href: getLocalizedPath('/shop') },
    { name: t('coffeeBoxBuilder'), href: getLocalizedPath('/coffee-box-builder') },
    { name: t('bundles'), href: getLocalizedPath('/bundles') },
    { name: t('gifts'), href: getLocalizedPath('/regali') },
    { name: t('about'), href: getLocalizedPath('/about') },
  ];

  // Debug logging
  if (typeof window !== 'undefined') {
    console.log('Current locale:', locale);
    console.log('Safe locale:', safeLocale);
    console.log('Navigation links:', navigation);
  }

  useEffect(() => {
    // Only initialize Supabase client on the client side
    if (typeof window === 'undefined') return;

    try {
      const supabase = createClient();

      // Get initial user
      const getUser = async () => {
        try {
          const user = await safeGetUser(supabase);
          setUser(user);
          if (user) {
            fetchIsAdmin(user.id);
          } else {
            setIsAdmin(false);
          }
        } catch (error) {
          // Only log unexpected errors, not normal guest user scenarios
          if (error instanceof Error &&
              !error.message.includes('Auth session missing') &&
              !error.message.includes('Invalid Refresh Token')) {
            console.error('Error getting user:', error);
          }
        }
      };
      getUser();

      // Listen for auth changes
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        (event, session) => {
          setUser(session?.user ?? null);
          if (session?.user) {
            fetchIsAdmin(session.user.id);
          } else {
            setIsAdmin(false);
          }
        }
      );

      return () => subscription.unsubscribe();
    } catch (error) {
      console.error('Error initializing Supabase client:', error);
    }
  }, []);

  const handleSignOut = async () => {
    try {
      const supabase = createClient();
      await supabase.auth.signOut();
      setIsUserMenuOpen(false);

      setIsAdmin(false);

      router.push(`/${safeLocale}`);

    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-gradient-to-r from-background/95 via-background/98 to-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 shadow-soft transition-shadow-smooth">
      <div className="container mx-auto px-0 md:px-4">
        <div className="flex h-16 items-center">
          {/* Logo */}
          <div className="flex-1">
            <Link href={`/${locale}`} className="flex items-center hover-scale group w-fit">
              <Image
                src="/logo.png"
                alt="PrimeCaffe logo"
                width={160}
                height={40}
                className="h-10 w-auto object-contain transition-all-smooth group-hover:opacity-80"
              />
            </Link>
          </div>

          {/* Desktop Navigation - Centered */}
          <nav className="hidden lg:flex items-center justify-center space-x-8 flex-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-sm font-medium transition-all-smooth hover:text-primary hover:scale-105 relative group"
              >
                <span className="relative z-10">{item.name}</span>
                <span className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 rounded-md scale-0 group-hover:scale-100 transition-transform-smooth -z-10"></span>
              </Link>
            ))}
          </nav>

          {/* Tablet Navigation - Clean Icon + Text Design */}
          <nav className="hidden md:flex lg:hidden items-center justify-center flex-1">
            <div className="flex items-center space-x-1">
              {/* Shop */}
              <Link
                href={`/${locale}/shop`}
                className="flex flex-col items-center px-2 py-2 text-xs font-medium transition-all-smooth hover:text-primary hover:scale-105 relative group rounded-lg"
              >
                <ShoppingBag className="h-4 w-4 mb-1" />
                <span className="relative z-10">Shop</span>
                <span className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg scale-0 group-hover:scale-100 transition-transform-smooth -z-10"></span>
              </Link>

              {/* Coffee Box Builder */}
              <Link
                href={`/${locale}/coffee-box-builder`}
                className="flex flex-col items-center px-2 py-2 text-xs font-medium transition-all-smooth hover:text-primary hover:scale-105 relative group rounded-lg"
              >
                <Package className="h-4 w-4 mb-1" />
                <span className="relative z-10">Builder</span>
                <span className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg scale-0 group-hover:scale-100 transition-transform-smooth -z-10"></span>
              </Link>

              {/* Bundles */}
              <Link
                href={`/${locale}/bundles`}
                className="flex flex-col items-center px-2 py-2 text-xs font-medium transition-all-smooth hover:text-primary hover:scale-105 relative group rounded-lg"
              >
                <Gift className="h-4 w-4 mb-1" />
                <span className="relative z-10">Box</span>
                <span className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg scale-0 group-hover:scale-100 transition-transform-smooth -z-10"></span>
              </Link>

              {/* Gifts */}
              <Link
                href={`/${locale}/regali`}
                className="flex flex-col items-center px-2 py-2 text-xs font-medium transition-all-smooth hover:text-primary hover:scale-105 relative group rounded-lg"
              >
                <Heart className="h-4 w-4 mb-1" />
                <span className="relative z-10">Regali</span>
                <span className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg scale-0 group-hover:scale-100 transition-transform-smooth -z-10"></span>
              </Link>

              {/* About */}
              <Link
                href={`/${locale}/about`}
                className="flex flex-col items-center px-2 py-2 text-xs font-medium transition-all-smooth hover:text-primary hover:scale-105 relative group rounded-lg"
              >
                <Info className="h-4 w-4 mb-1" />
                <span className="relative z-10">Chi siamo</span>
                <span className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg scale-0 group-hover:scale-100 transition-transform-smooth -z-10"></span>
              </Link>
            </div>
          </nav>

          {/* Right side actions */}
          <div className="flex items-center justify-end space-x-4 flex-1">
            {/* Language Switcher */}
            <LanguageSwitcher />

            {/* Cart */}
            <CartButton />

            {/* User Account */}
            {user ? (
              <div className="relative">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="relative"
                >
                  <User className="h-5 w-5" />
                  {pendingCount > 0 && (
                    <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      {pendingCount > 9 ? '9+' : pendingCount}
                    </span>
                  )}
                  <span className="sr-only">{t('account')}</span>
                </Button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border z-50">
                    <div className="py-1">
                      <div className="px-4 py-2 text-sm text-gray-700 border-b">
                        {user.email}
                      </div>
                      <Link
                        href={`/${locale}/account`}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        {t('account')}
                      </Link>
                      {isAdmin && (
                        <>
                          <Link
                            href={`/${locale}/admin`}
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => setIsUserMenuOpen(false)}
                          >
                            <LayoutDashboard className="mr-2 h-4 w-4" />
                            {t('admin')}
                          </Link>
                          <Link
                            href={`/${locale}/admin/settings`}
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => setIsUserMenuOpen(false)}
                          >
                            <Cog className="mr-2 h-4 w-4" />
                            {t('settings')}
                          </Link>
                        </>
                      )}
                      <button
                        onClick={handleSignOut}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        {t('logout')}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <Button variant="ghost" size="sm" asChild>
                <Link href={`/${locale}/login`}>
                  {t('login')}
                </Link>
              </Button>
            )}

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
              <span className="sr-only">{t('menu')}</span>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 border-t">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-base font-medium transition-colors hover:text-primary"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        )}


      </div>
    </header>
  );
}
