@import "tailwindcss";

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 25 95% 53%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 25 95% 53%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --radius: 0.5rem;

  /* Enhanced Visual Variables */
  --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
  --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--accent)) 100%);
  --gradient-hero: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--background)) 50%, hsl(var(--secondary) / 0.1) 100%);
  --gradient-card: linear-gradient(145deg, hsl(var(--card)) 0%, hsl(var(--muted) / 0.3) 100%);
  --shadow-soft: 0 2px 8px -2px hsl(var(--foreground) / 0.1);
  --shadow-medium: 0 4px 16px -4px hsl(var(--foreground) / 0.15);
  --shadow-strong: 0 8px 32px -8px hsl(var(--foreground) / 0.2);
  --shadow-glow: 0 0 20px hsl(var(--primary) / 0.3);
  --shadow-glow-hover: 0 0 30px hsl(var(--primary) / 0.4);
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --font-sans: system-ui, sans-serif;
  --font-mono: monospace;
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-sans);
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: var(--shadow-glow);
  }
  50% {
    box-shadow: var(--shadow-glow-hover);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes gradient-x-slow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Enhanced Utility Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, hsl(var(--muted) / 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}

.animate-gradient-x-slow {
  animation: gradient-x-slow 6s ease infinite;
}

/* Enhanced Shadows */
.shadow-soft {
  box-shadow: var(--shadow-soft);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-strong {
  box-shadow: var(--shadow-strong);
}

.shadow-glow {
  box-shadow: var(--shadow-glow);
}

.shadow-glow-hover {
  box-shadow: var(--shadow-glow-hover);
}

/* Enhanced Gradients */
.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-hero {
  background: var(--gradient-hero);
}

.bg-gradient-card {
  background: var(--gradient-card);
}

/* Enhanced Transitions */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform-smooth {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-shadow-smooth {
  transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-strong);
}

.hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow-hover);
}

.hover-scale {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Liquid Glass Cookie Banner Styles */
.liquid-glass-container {
  position: relative;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.liquid-glass-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(147, 51, 234, 0.1) 50%,
    rgba(236, 72, 153, 0.1) 100%
  );
  border-radius: 24px;
  z-index: -1;
}

.liquid-glass-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.liquid-glass-icon-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-glass-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
}

.liquid-glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.liquid-glass-button-primary {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.3) 0%,
    rgba(147, 51, 234, 0.3) 100%
  );
  color: white;
  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-glass-button-primary::before {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.4) 0%,
    rgba(147, 51, 234, 0.4) 100%
  );
  opacity: 0;
}

.liquid-glass-button-primary:hover::before {
  opacity: 1;
}

.liquid-glass-button-primary:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.liquid-glass-button-secondary {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  color: white;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-glass-button-secondary::before {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  opacity: 0;
}

.liquid-glass-button-secondary:hover::before {
  opacity: 1;
}

.liquid-glass-button-secondary:hover {
  transform: translateY(-1px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.liquid-glass-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.04) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 16px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Tablet Navigation Improvements */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Tablet header adjustments */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Tablet navigation icon + text design */
  nav a {
    min-width: 50px;
    text-align: center;
    font-size: 11px;
  }

  /* Tablet navigation hover effects */
  nav a:hover {
    transform: translateY(-1px);
  }

  /* Tablet navigation spacing optimization */
  nav .flex.items-center {
    gap: 2px;
  }

  /* Tablet icon sizing */
  nav svg {
    width: 14px;
    height: 14px;
  }
}

/* Mobile Responsive Improvements */
@media (max-width: 768px) {
  .liquid-glass-container {
    padding: 20px;
    border-radius: 20px;
    margin: 0 8px;
  }

  .liquid-glass-button {
    padding: 14px 18px;
    font-size: 15px;
    border-radius: 14px;
  }

  .liquid-glass-card {
    padding: 14px;
    border-radius: 14px;
  }

  .liquid-glass-icon {
    width: 44px;
    height: 44px;
    border-radius: 14px;
  }

  .liquid-glass-icon-small {
    width: 32px;
    height: 32px;
    border-radius: 10px;
  }

  /* Account page tabs mobile optimization */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Mobile tab animations */
  [data-radix-tabs-trigger] {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  [data-radix-tabs-trigger][data-state="active"] {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Smooth scroll behavior for mobile tabs */
  .overflow-x-auto {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Enhanced touch targets for mobile */
  [data-radix-tabs-trigger] {
    min-height: 64px;
    min-width: 80px;
    touch-action: manipulation;
  }
}

/* Enhanced Focus States for Accessibility */
.liquid-glass-button:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

button:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

/* Smooth Backdrop Animation */
@keyframes backdropFadeIn {
  from {
    backdrop-filter: blur(0px);
    background-color: rgba(0, 0, 0, 0);
  }
  to {
    backdrop-filter: blur(8px);
    background-color: rgba(0, 0, 0, 0.2);
  }
}

@keyframes slideUpFadeIn {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Performance Optimizations */
.liquid-glass-container,
.liquid-glass-button,
.liquid-glass-card,
.liquid-glass-icon,
.liquid-glass-icon-small {
  will-change: transform;
  transform: translateZ(0);
}

/* Avoid mobile browser zoom on form inputs */
@media (max-width: 768px) {
  input,
  textarea,
  select {
    font-size: 16px;
  }
}
